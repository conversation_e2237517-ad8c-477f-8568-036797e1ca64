import React, { useState, useEffect, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import {
  Upload,
  X,
  Image as ImageIcon,
  AlertCircle,
  Calculator,
  Percent
} from 'lucide-react'
import {
  useActiveProductCategories,
  useCreateProduct,
  useUpdateProduct
} from '@/hooks/queries'
import { useProductValidation } from '@/hooks/useProductValidation'
import type { ProductWithStock, ProductFormData } from '@/types/inventory'
import { UNIT_OF_MEASURE_OPTIONS } from '@/types/inventory'

const productFormSchema = z.object({
  sku: z.string()
    .min(1, 'SKU is required')
    .max(100, 'SKU must be 100 characters or less')
    .regex(/^[A-Za-z0-9\-_]+$/, 'SKU can only contain letters, numbers, hyphens, and underscores'),
  name: z.string().min(1, 'Product name is required').max(255, 'Name must be 255 characters or less'),
  description: z.string().max(1000, 'Description must be 1000 characters or less').optional(),
  category_id: z.string().optional(),
  unit_of_measure: z.string().min(1, 'Unit of measure is required'),
  cost_price: z.number().min(0, 'Cost price must be positive').default(0),
  selling_price: z.number().min(0, 'Selling price must be positive').default(0),
  track_inventory: z.boolean().default(true),
  reorder_level: z.number().min(0, 'Reorder level must be positive').default(0),
  reorder_quantity: z.number().min(0, 'Reorder quantity must be positive').default(0),
  barcode: z.string().max(255, 'Barcode must be 255 characters or less').optional(),
  weight: z.number().min(0, 'Weight must be positive').optional().nullable(),
  dimensions: z.string().max(100, 'Dimensions must be 100 characters or less').optional(),
  is_active: z.boolean().default(true),
  is_sellable: z.boolean().default(true),
  is_purchasable: z.boolean().default(true),
}).refine((data) => {
  // Validate that selling price is not less than cost price (with warning)
  if (data.cost_price > 0 && data.selling_price > 0 && data.selling_price < data.cost_price) {
    return false
  }
  return true
}, {
  message: "Selling price should not be less than cost price",
  path: ["selling_price"]
})

type ProductFormValues = z.infer<typeof productFormSchema>

interface ProductFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  product?: ProductWithStock | null
  onSuccess?: () => void
}

export function ProductForm({ open, onOpenChange, product, onSuccess }: ProductFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [showPricingHelp, setShowPricingHelp] = useState(false)
  const [validationErrors, setValidationErrors] = useState<Record<string, string[]>>({})
  const [validationWarnings, setValidationWarnings] = useState<Record<string, string[]>>({})
  const [skuSuggestions, setSkuSuggestions] = useState<string[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const { data: categories = [] } = useActiveProductCategories()
  const createProduct = useCreateProduct()
  const updateProduct = useUpdateProduct()

  // Enhanced validation
  const {
    validateProductData,
    validateSingleField,
    isSkuUnique,
    validateSkuFormat,
    validatePricing,
    calculateMargin,
    generateSkuSuggestions
  } = useProductValidation({ currentProductId: product?.id })

  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productFormSchema),
    defaultValues: {
      sku: '',
      name: '',
      description: '',
      category_id: 'none',
      unit_of_measure: 'each',
      cost_price: 0,
      selling_price: 0,
      track_inventory: true,
      reorder_level: 0,
      reorder_quantity: 0,
      barcode: '',
      weight: null,
      dimensions: '',
      is_active: true,
      is_sellable: true,
      is_purchasable: true,
    },
  })

  // Reset form when product changes
  useEffect(() => {
    if (product) {
      form.reset({
        sku: product.sku,
        name: product.name,
        description: product.description || '',
        category_id: product.category_id || 'none',
        unit_of_measure: product.unit_of_measure,
        cost_price: Number(product.cost_price) || 0,
        selling_price: Number(product.selling_price) || 0,
        track_inventory: product.track_inventory ?? true,
        reorder_level: Number(product.reorder_level) || 0,
        reorder_quantity: Number(product.reorder_quantity) || 0,
        barcode: product.barcode || '',
        weight: product.weight ? Number(product.weight) : null,
        dimensions: product.dimensions || '',
        is_active: product.is_active ?? true,
        is_sellable: product.is_sellable ?? true,
        is_purchasable: product.is_purchasable ?? true,
      })
    } else {
      form.reset({
        sku: '',
        name: '',
        description: '',
        category_id: '',
        unit_of_measure: 'each',
        cost_price: 0,
        selling_price: 0,
        track_inventory: true,
        reorder_level: 0,
        reorder_quantity: 0,
        barcode: '',
        weight: null,
        dimensions: '',
        is_active: true,
        is_sellable: true,
        is_purchasable: true,
      })
    }
  }, [product, form])

  const onSubmit = async (values: ProductFormValues) => {
    setIsSubmitting(true)
    try {
      const formData: ProductFormData = {
        ...values,
        description: values.description || '',
        category_id: values.category_id === 'none' ? '' : values.category_id,
        barcode: values.barcode || '',
        dimensions: values.dimensions || '',
      }

      // Run comprehensive validation
      const validationResult = validateProductData(formData)

      if (!validationResult.isValid) {
        setValidationErrors(validationResult.errors)
        setValidationWarnings(validationResult.warnings)

        // Show first error in form
        const firstErrorField = Object.keys(validationResult.errors)[0]
        if (firstErrorField) {
          form.setError(firstErrorField as any, {
            message: validationResult.errors[firstErrorField][0]
          })
        }

        setIsSubmitting(false)
        return
      }

      // Clear validation errors if validation passes
      setValidationErrors({})
      setValidationWarnings(validationResult.warnings)

      if (product) {
        await updateProduct.mutateAsync({
          productId: product.id,
          productData: formData,
        })
      } else {
        await createProduct.mutateAsync(formData)
      }

      onOpenChange(false)
      onSuccess?.()
    } catch (error) {
      console.error('Failed to save product:', error)
      // Handle API errors
      if (error instanceof Error) {
        if (error.message.includes('SKU')) {
          form.setError('sku', { message: 'SKU already exists or is invalid' })
        } else if (error.message.includes('barcode')) {
          form.setError('barcode', { message: 'Barcode already exists or is invalid' })
        } else {
          form.setError('root', { message: 'Failed to save product. Please try again.' })
        }
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const trackInventory = form.watch('track_inventory')
  const costPrice = form.watch('cost_price')
  const sellingPrice = form.watch('selling_price')

  // Calculate margin percentage
  const marginPercentage = costPrice > 0 && sellingPrice > 0
    ? ((sellingPrice - costPrice) / sellingPrice * 100).toFixed(1)
    : '0'

  // Image handling functions
  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file')
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('Image size must be less than 5MB')
        return
      }

      setImageFile(file)

      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleImageRemove = () => {
    setImageFile(null)
    setImagePreview(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const generateSKU = () => {
    const name = form.getValues('name')
    const categoryId = form.getValues('category_id')

    if (name) {
      const suggestions = generateSkuSuggestions(name, categoryId === 'none' ? undefined : categoryId)
      if (suggestions.length > 0) {
        form.setValue('sku', suggestions[0])
        setSkuSuggestions(suggestions.slice(1))
      }
    }
  }

  // Real-time SKU validation
  const handleSkuChange = (value: string) => {
    const formatValidation = validateSkuFormat(value)
    const isUnique = isSkuUnique(value)

    if (!formatValidation.isValid) {
      setValidationErrors(prev => ({
        ...prev,
        sku: [formatValidation.message!]
      }))
    } else if (!isUnique) {
      setValidationErrors(prev => ({
        ...prev,
        sku: ['SKU already exists. Please choose a unique SKU.']
      }))
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors.sku
        return newErrors
      })
    }
  }

  // Real-time pricing validation
  const handlePricingChange = () => {
    const costPrice = form.getValues('cost_price') || 0
    const sellingPrice = form.getValues('selling_price') || 0

    const pricingValidation = validatePricing(costPrice, sellingPrice)

    if (pricingValidation.errors.length > 0) {
      setValidationErrors(prev => ({
        ...prev,
        selling_price: pricingValidation.errors
      }))
    } else {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors.selling_price
        return newErrors
      })
    }

    if (pricingValidation.warnings.length > 0) {
      setValidationWarnings(prev => ({
        ...prev,
        selling_price: pricingValidation.warnings
      }))
    } else {
      setValidationWarnings(prev => {
        const newWarnings = { ...prev }
        delete newWarnings.selling_price
        return newWarnings
      })
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {product ? 'Edit Product' : 'Create New Product'}
          </DialogTitle>
          <DialogDescription>
            {product 
              ? 'Update the product information below.' 
              : 'Add a new product to your inventory.'
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Basic Information</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="sku"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SKU *</FormLabel>
                      <div className="flex gap-2">
                        <FormControl>
                          <Input
                            placeholder="Enter SKU"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e)
                              handleSkuChange(e.target.value)
                            }}
                            className={validationErrors.sku ? 'border-red-500' : ''}
                          />
                        </FormControl>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={generateSKU}
                          disabled={!form.watch('name')}
                        >
                          Generate
                        </Button>
                      </div>
                      <FormDescription>
                        Unique identifier for this product
                      </FormDescription>

                      {/* Validation Errors */}
                      {validationErrors.sku && (
                        <div className="text-sm text-red-600 mt-1">
                          {validationErrors.sku.map((error, index) => (
                            <div key={index} className="flex items-center gap-1">
                              <AlertCircle className="h-3 w-3" />
                              {error}
                            </div>
                          ))}
                        </div>
                      )}

                      {/* SKU Suggestions */}
                      {skuSuggestions.length > 0 && !validationErrors.sku && (
                        <div className="mt-2">
                          <div className="text-xs text-gray-600 mb-1">Suggestions:</div>
                          <div className="flex flex-wrap gap-1">
                            {skuSuggestions.slice(0, 3).map((suggestion, index) => (
                              <Button
                                key={index}
                                type="button"
                                variant="outline"
                                size="sm"
                                className="h-6 text-xs"
                                onClick={() => {
                                  form.setValue('sku', suggestion)
                                  setSkuSuggestions([])
                                }}
                              >
                                {suggestion}
                              </Button>
                            ))}
                          </div>
                        </div>
                      )}

                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="barcode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Barcode</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter barcode" {...field} />
                      </FormControl>
                      <FormDescription>
                        UPC, EAN, or other barcode
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Product Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter product name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Enter product description" 
                        rows={3}
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="category_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">No Category</SelectItem>
                          {categories.map((category) => (
                            <SelectItem key={category.id} value={category.id}>
                              {category.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="unit_of_measure"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Unit of Measure *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select unit" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {UNIT_OF_MEASURE_OPTIONS.map((unit) => (
                            <SelectItem key={unit} value={unit}>
                              {unit}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Product Image */}
              <div>
                <FormLabel>Product Image</FormLabel>
                <div className="mt-2">
                  {imagePreview ? (
                    <div className="relative inline-block">
                      <img
                        src={imagePreview}
                        alt="Product preview"
                        className="w-32 h-32 object-cover rounded-lg border"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                        onClick={handleImageRemove}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ) : (
                    <div
                      className="w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-gray-400 transition-colors"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      <ImageIcon className="h-8 w-8 text-gray-400 mb-2" />
                      <span className="text-sm text-gray-500">Upload Image</span>
                    </div>
                  )}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageSelect}
                    className="hidden"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    Recommended: 400x400px, max 5MB (JPG, PNG, WebP)
                  </p>
                </div>
              </div>
            </div>

            <Separator />

            {/* Pricing */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">Pricing</h3>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPricingHelp(!showPricingHelp)}
                >
                  <Calculator className="h-4 w-4 mr-1" />
                  Margin: {marginPercentage}%
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="cost_price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cost Price</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => {
                            field.onChange(parseFloat(e.target.value) || 0)
                            setTimeout(handlePricingChange, 100)
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        What you pay for this product
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="selling_price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Selling Price</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => {
                            field.onChange(parseFloat(e.target.value) || 0)
                            setTimeout(handlePricingChange, 100)
                          }}
                        />
                      </FormControl>
                      <FormDescription>
                        What you charge customers
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Pricing Warnings */}
              {costPrice > 0 && sellingPrice > 0 && (
                <div className="space-y-2">
                  {sellingPrice < costPrice && (
                    <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <AlertCircle className="h-4 w-4 text-red-600" />
                      <span className="text-sm text-red-700">
                        Warning: Selling price is below cost price. You'll lose money on each sale.
                      </span>
                    </div>
                  )}

                  {parseFloat(marginPercentage) < 20 && sellingPrice >= costPrice && (
                    <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                      <AlertCircle className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm text-yellow-700">
                        Low margin: Consider increasing the selling price for better profitability.
                      </span>
                    </div>
                  )}

                  {showPricingHelp && (
                    <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="text-sm text-blue-700 space-y-1">
                        <div className="font-medium">Pricing Calculations:</div>
                        <div>• Margin: UGX {(sellingPrice - costPrice).toLocaleString()} ({marginPercentage}%)</div>
                        <div>• Markup: {costPrice > 0 ? ((sellingPrice - costPrice) / costPrice * 100).toFixed(1) : '0'}%</div>
                        <div className="text-xs mt-2 text-blue-600">
                          Margin = (Selling Price - Cost Price) / Selling Price × 100
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            <Separator />

            {/* Inventory Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Inventory Settings</h3>
              
              <FormField
                control={form.control}
                name="track_inventory"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Track Inventory</FormLabel>
                      <FormDescription>
                        Enable inventory tracking for this product
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              {trackInventory && (
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="reorder_level"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reorder Level</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.001" 
                            min="0"
                            placeholder="0" 
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormDescription>
                          Alert when stock falls below this level
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="reorder_quantity"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reorder Quantity</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.001" 
                            min="0"
                            placeholder="0" 
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormDescription>
                          Suggested quantity to reorder
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              )}
            </div>

            <Separator />

            {/* Physical Properties */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Physical Properties</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="weight"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Weight (kg)</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          step="0.001" 
                          min="0"
                          placeholder="0.000" 
                          {...field}
                          value={field.value || ''}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : null)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dimensions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Dimensions</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., 10x5x3 cm" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Separator />

            {/* Status Settings */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Status Settings</h3>
              
              <div className="space-y-3">
                <FormField
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Active</FormLabel>
                        <FormDescription>
                          Product is active and visible in the system
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="is_sellable"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Sellable</FormLabel>
                        <FormDescription>
                          Product can be sold to customers
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="is_purchasable"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Purchasable</FormLabel>
                        <FormDescription>
                          Product can be purchased from vendors
                        </FormDescription>
                      </div>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : (product ? 'Update Product' : 'Create Product')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
