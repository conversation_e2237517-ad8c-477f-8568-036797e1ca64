{"name": "kaya-finance", "private": true, "version": "1.0.0", "description": "Modern Financial Management for Small & Medium Enterprises", "type": "module", "author": "Kaya Finance Team", "license": "MIT", "homepage": "https://github.com/your-username/kaya-finance#readme", "repository": {"type": "git", "url": "git+https://github.com/your-username/kaya-finance.git"}, "bugs": {"url": "https://github.com/your-username/kaya-finance/issues"}, "keywords": ["finance", "accounting", "sme", "uganda", "africa", "react", "typescript", "supabase"], "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:staging": "vite build --mode staging", "build:prod": "vite build --mode production", "build:production": "./scripts/production-build.sh", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "tsc --noEmit", "preview": "vite preview", "preview:staging": "vite preview --mode staging", "preview:prod": "vite preview --mode production", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:backup": "jest --test<PERSON>athPattern=backup", "test:unit": "jest --testPathPattern=src/__tests__/.*\\.test\\.(ts|tsx)$", "test:integration": "jest --testPathPattern=src/__tests__/integration/.*\\.test\\.(ts|tsx)$", "test:critical": "jest --testPathPattern=src/__tests__/critical/.*\\.test\\.(ts|tsx)$", "test:smoke": "jest --testPathPattern=src/__tests__/smoke/.*\\.test\\.(ts|tsx)$", "test:smoke:deployed": "jest --testPathPattern=src/__tests__/smoke/.*\\.deployed\\.test\\.(ts|tsx)$", "test:health": "node scripts/health-check.js", "production:deploy": "./scripts/production-deploy.sh", "production:health-check": "node scripts/health-check-production.js", "security:audit": "npm audit --audit-level=high", "security:fix": "npm audit fix", "test:a11y": "jest --testPathPattern=src/__tests__/accessibility/.*\\.test\\.(ts|tsx)$", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "analyze": "npx vite-bundle-analyzer dist/stats.html", "security:check": "npx audit-ci --moderate", "deploy:staging": "./scripts/deploy.sh staging", "deploy:production": "./scripts/deploy.sh production", "deploy:force": "./scripts/deploy.sh production --force", "db:migrate": "npx supabase db push", "db:reset": "npx supabase db reset", "db:seed": "npx supabase db seed"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@sentry/react": "^9.33.0", "@sentry/tracing": "^7.120.3", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.56.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "jspdf": "^3.0.1", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.12.7", "resend": "^4.5.2", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "terser": "^5.43.1", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@playwright/test": "^1.53.1", "@tailwindcss/typography": "^0.5.15", "@tanstack/react-query-devtools": "^5.80.6", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/jspdf": "^1.3.3", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-window": "^1.8.8", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.2", "postcss": "^8.4.47", "prettier": "^3.6.2", "tailwindcss": "^3.4.11", "ts-jest": "^29.3.4", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vite-bundle-analyzer": "^0.23.0"}}